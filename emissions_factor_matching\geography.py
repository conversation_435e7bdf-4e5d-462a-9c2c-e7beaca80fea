from emissions_factor_matching.dataset import efs_with_geographies
from utils import logger


class Geography:
    def __init__(self, names: str | list[str], parent=None):
        if isinstance(names, str):
            self.names = [names]
        else:
            self.names = names
        self.children = []
        self.parent = parent

    def add_child(self, child_names: str | list[str]):
        """Add a child geography."""
        self.children.append(
            Geography(child_names, parent=self),
        )
        return Geography(child_names, self)

    def get_priority_geographies(self):
        """Get a list of geographies by priority from the current geography up to the root."""
        priority_list = []
        current = self
        while current:
            for name in current.names:
                priority_list.append(name)
            current = current.parent
        return priority_list

    def find_geography(self, name):
        """Find a geography by name."""
        if name in self.names:
            return self
        for child in self.children:
            found = child.find_geography(name)
            if found:
                return found
        return None

top_level_geographies = [
    "GLO",
    "RoW",
]

continents = [
    "RER",
    "RNA",
    "RAF",
    "RSA",
    "RAS",
    "ROC",
]

europe_iso_codes = [
    "AL", "AD", "AM", "AT", "AZ", "BY", "BE", "BA", "BG", "HR", "CY", "CZ",
    "DK", "EE", "FI", "FR", "GE", "DE", "GR", "HU", "IS", "IE", "IT", "KZ",
    "XK", "LV", "LI", "LT", "LU", "MT", "MD", "MC", "ME", "NL", "MK", "NO",
    "PL", "PT", "RO", "RU", "SM", "RS", "SK", "SI", "ES", "SE", "CH", "TR",
    "UA", "GB", "VA"
]

asia_iso_codes = [
    "AF", "AM", "AZ", "BH", "BD", "BT", "BN", "KH", "CN", "CY", "GE", "IN",
    "ID", "IR", "IQ", "IL", "JP", "JO", "KZ", "KP", "KR", "KW", "KG", "LA",
    "LB", "MO", "MY", "MV", "MN", "MM", "NP", "OM", "PK", "PS", "PH", "QA",
    "SA", "SG", "LK", "SY", "TW", "TJ", "TH", "TL", "TR", "TM", "AE", "UZ",
    "VN", "YE"
]

africa_iso_codes = [
    "DZ", "AO", "BJ", "BW", "BF", "BI", "CV", "CM", "CF", "TD", "KM", "CD",
    "CG", "CI", "DJ", "EG", "GQ", "ER", "SZ", "ET", "GA", "GM", "GH", "GN",
    "GW", "KE", "LS", "LR", "LY", "MG", "MW", "ML", "MR", "MU", "YT", "MA",
    "MZ", "NA", "NE", "NG", "RE", "RW", "SH", "ST", "SN", "SC", "SL", "SO",
    "ZA", "SS", "SD", "TZ", "TG", "TN", "UG", "EH", "ZM", "ZW"
]

north_america_iso_codes = [
    "AG", "AI", "AW", "BS", "BB", "BZ", "BM", "CA", "KY", "CR", "CU", "CW",
    "DM", "DO", "SV", "GL", "GD", "GP", "GT", "HT", "HN", "JM", "MQ", "MX",
    "MS", "NI", "PA", "PR", "BL", "KN", "LC", "MF", "PM", "VC", "SX", "TT",
    "TC", "US", "VG", "VI"
]

south_america_iso_codes = [
    "AR", "BO", "BR", "CL", "CO", "EC", "FK", "GF", "GY", "PY", "PE", "SR",
    "UY", "VE"
]

oceania_iso_codes = [
    "AS", "AU", "CK", "FJ", "PF", "GU", "KI", "MH", "FM", "NR", "NC", "NZ",
    "NU", "NF", "MP", "PW", "PG", "PN", "WS", "SB", "TK", "TO", "TV", "UM",
    "VU", "WF"
]

root = Geography(top_level_geographies)
for continent in continents:
    root.add_child(continent)

europe = root.find_geography("RER")
for country in europe_iso_codes:
    europe.add_child(country)

asia = root.find_geography("RAS")
for country in asia_iso_codes:
    asia.add_child(country)

africa = root.find_geography("RAF")
for country in africa_iso_codes:
    africa.add_child(country)

north_america = root.find_geography("RNA")
for country in north_america_iso_codes:
    north_america.add_child(country)

south_america = root.find_geography("RSA")
for country in south_america_iso_codes:
    south_america.add_child(country)

oceana = root.find_geography("ROC")
for country in oceania_iso_codes:
    oceana.add_child(country)

def get_geography_matches_with_priority(iso_code):
    geography = root.find_geography(iso_code)
    if geography is None:
        logger.warning(f"Geography hierarchy: ISO code '{iso_code}' not found in hierarchy, falling back to GLO")
        # Fallback to GLO if the ISO code is not found
        geography = root.find_geography("GLO")
        if geography is None:
            # If even GLO is not found, return a default list
            logger.error("Geography hierarchy: GLO not found in hierarchy, using default list")
            return ["GLO", "RoW"]
    
    priority_list = geography.get_priority_geographies()
    logger.debug(f"Geography hierarchy: Priority list for '{iso_code}': {priority_list}")
    return priority_list

def get_geography_activity_match(activity_name: str, iso_code: str, reference_product: str | None=None, preferred_source: str | None=None):
    logger.info(f"Geography matching: Requested '{iso_code}' for activity '{activity_name}'")
    if preferred_source:
        logger.info(f"Geography matching: Preferred source specified: '{preferred_source}'")
    
    geography_matches = get_geography_matches_with_priority(iso_code)
    logger.info(f"Geography matching: Priority list for '{iso_code}': {geography_matches}")
    
    regex_pattern = f"^{iso_code}|{iso_code}-|-{iso_code}$"

    for geography_match in geography_matches:
        regex_pattern = f"^{geography_match}|{geography_match}-|-{geography_match}$"
        logger.debug(f"Geography matching: Trying geography '{geography_match}' with pattern '{regex_pattern}'")
        
        if reference_product:
            matches = efs_with_geographies[
                (efs_with_geographies['Activity Name'] == activity_name) &
                (efs_with_geographies['Reference Product Name'] == reference_product) &
                efs_with_geographies['Geography'].str.contains(regex_pattern, regex=True)
            ]
        else:
            matches = efs_with_geographies[
                (efs_with_geographies['Activity Name'] == activity_name) &
                efs_with_geographies['Geography'].str.contains(regex_pattern, regex=True)
            ]

        if not matches.empty:
            logger.info(f"Geography matching: Found {len(matches)} matches for geography '{geography_match}'")
            
            # If preferred_source is specified, try to find a match with that source first
            if preferred_source:
                preferred_matches = matches[matches['Source'] == preferred_source]
                if not preferred_matches.empty:
                    logger.info(f"Geography matching: Found preferred source '{preferred_source}' for activity '{activity_name}' with geography '{geography_match}'")
                    return preferred_matches.iloc[0]
                else:
                    logger.warning(f"Geography matching: Preferred source '{preferred_source}' not found for geography '{geography_match}', using first available")
                    logger.info(f"Geography matching: Using source '{matches.iloc[0]['Source']}' instead")

            return matches.iloc[0]

    # Geography fallback: Try without geography constraint
    logger.warning(f"Geography fallback: No matches found in priority list {geography_matches}")
    logger.info(f"Geography fallback: Searching for activity without geography constraint")
    
    activity_matchs = efs_with_geographies[
        (efs_with_geographies['Activity Name'] == activity_name) &
        (efs_with_geographies['Reference Product Name'] == reference_product)
    ]

    logger.warning(f"Geography fallback: Found {len(activity_matchs)} matches without geography constraint")
    logger.warning(f"Available geographies for '{activity_name}':")

    for _, activity in activity_matchs.iterrows():
        logger.warning(activity["Geography"])

    # Even in fallback, try to respect preferred source if specified
    if preferred_source and not activity_matchs.empty:
        preferred_fallback = activity_matchs[activity_matchs['Source'] == preferred_source]
        if not preferred_fallback.empty:
            selected_geography = preferred_fallback.iloc[0]['Geography']
            logger.info(f"Geography fallback: Using preferred source '{preferred_source}' with geography '{selected_geography}'")
            return preferred_fallback.iloc[0]
        else:
            logger.warning(f"Geography fallback: Preferred source '{preferred_source}' not found, using first available")

    if not activity_matchs.empty:
        selected_geography = activity_matchs.iloc[0]['Geography']
        selected_source = activity_matchs.iloc[0]['Source']
        logger.info(f"Geography fallback: Final selection - geography '{selected_geography}', source '{selected_source}'")
        return activity_matchs.iloc[0]
    else:
        logger.error(f"Geography fallback: No matches found for activity '{activity_name}'")
        raise ValueError(f"No matches found for activity '{activity_name}'")
